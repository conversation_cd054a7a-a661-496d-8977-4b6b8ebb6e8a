<template>
  <div class="chat-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <el-tabs v-model="activeTab" class="sidebar-tabs">
          <el-tab-pane label="聊天" name="chat">
            <el-input
              v-model="searchText"
              placeholder="搜索好友"
              prefix-icon="el-icon-search"
              size="small"
              class="search-input"
            />
          </el-tab-pane>
          <el-tab-pane name="contacts">
            <span slot="label">
              通讯录
              <el-badge
                v-if="pendingRequestsCount > 0"
                :value="pendingRequestsCount"
                class="tab-badge"
              />
            </span>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 聊天列表 -->
      <div v-if="activeTab === 'chat'" class="content-list">
        <div class="list-header">
          <span>最近聊天</span>
          <el-button
            type="text"
            icon="el-icon-plus"
            class="create-group-btn"
            @click="showCreateGroupDialog = true"
          >
            创建群聊
          </el-button>
        </div>
        <div class="friends-list">
          <div
            v-for="friend in filteredFriends"
            :key="friend.id"
            class="friend-item"
            :class="{ active: currentFriend && currentFriend.id === friend.id }"
            @click="selectFriend(friend)"
            @contextmenu.prevent="handleRightClick(friend, $event)"
          >
            <el-avatar :src="friend.avatar" class="friend-avatar">
              {{ getDisplayName(friend).charAt(0) }}
            </el-avatar>
            <div class="friend-info">
              <!-- 在好友列表模板中 -->
              <div class="friend-name">
                {{ getDisplayName(friend) }}
                <span
                  v-if="groupRemarks[friend.id] && friend.type === 'group'"
                  class="real-name-small"
                >
                  ({{ friend.name }})
                </span>
                <el-tag v-if="friend.type === 'group'" size="mini" type="info"> 群 </el-tag>
              </div>

              <div class="last-message">
                {{ friend.lastMessage }}
              </div>
            </div>
            <div class="friend-meta">
              <div class="time">
                {{ friend.lastTime }}
              </div>
              <el-badge
                v-if="friend.unreadCount > 0"
                :value="friend.unreadCount"
                class="unread-badge"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 通讯录 -->
      <div v-else class="content-list contacts-container">
        <div class="contacts-header">
          <div class="contacts-actions">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click="showAddFriendDialog = true"
            >
              添加好友
            </el-button>
            <el-button
              type="success"
              size="small"
              icon="el-icon-chat-dot-square"
              @click="showJoinGroupDialog = true"
            >
              加入群聊
            </el-button>
          </div>
        </div>

        <div class="contact-sections">
          <!-- 好友申请 -->
          <div class="contact-section">
            <div class="section-header" @click="toggleSection('friendRequests')">
              <div class="section-title">
                <i class="el-icon-message section-icon" />
                <span>好友申请</span>
                <el-badge
                  v-if="friendRequests.length > 0"
                  :value="friendRequests.length"
                  class="section-badge"
                />
              </div>
              <i
                :class="[
                  'el-icon-arrow-right',
                  'section-arrow',
                  { expanded: expandedSections.friendRequests },
                ]"
              />
            </div>
            <div v-if="expandedSections.friendRequests" class="section-content">
              <div v-if="friendRequests.length === 0" class="empty-state">
                <i class="el-icon-chat-line-square" />
                <span>暂无好友申请</span>
              </div>
              <div v-for="request in friendRequests" :key="request.id" class="request-item">
                <el-avatar :src="request.avatar" size="medium">
                  {{ (request.name || '').charAt(0) }}
                </el-avatar>
                <div class="request-info">
                  <div class="request-name">
                    {{ request.name }}
                  </div>
                  <div class="request-message">
                    {{ request.message }}
                  </div>
                  <div class="request-time">
                    {{ formatTime(request.timestamp) }}
                  </div>
                </div>
                <div class="request-actions">
                  <el-button size="small" type="primary" @click="acceptFriendRequest(request)">
                    同意
                  </el-button>
                  <el-button size="small" @click="rejectFriendRequest(request)"> 拒绝 </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 群聊申请 -->
          <div v-if="groupRequests.length > 0" class="contact-section">
            <div class="section-header" @click="toggleSection('groupRequests')">
              <div class="section-title">
                <i class="el-icon-s-custom section-icon" />
                <span>群聊申请</span>
                <el-badge
                  v-if="groupRequests.length > 0"
                  :value="groupRequests.length"
                  class="section-badge"
                />
              </div>
              <i
                :class="[
                  'el-icon-arrow-right',
                  'section-arrow',
                  { expanded: expandedSections.groupRequests },
                ]"
              />
            </div>
            <div v-if="expandedSections.groupRequests" class="section-content">
              <div
                v-for="request in groupRequests"
                :key="request.id"
                class="request-item group-request-item"
              >
                <div class="group-request-header">
                  <el-avatar :src="request.applicantAvatar" size="small">
                    {{ (request.applicantName || '').charAt(0) }}
                  </el-avatar>
                  <div class="group-request-title">
                    <span class="applicant-name">{{ request.applicantName }}</span>
                    申请加入
                    <span class="group-name-inline">{{ request.groupName }}</span>
                  </div>
                  <div class="request-time-inline">
                    {{ formatTime(request.timestamp) }}
                  </div>
                </div>

                <div class="group-request-content">
                  <div class="request-reason">
                    <i class="el-icon-chat-dot-round reason-icon" />
                    {{ request.reason }}
                  </div>

                  <div class="request-actions compact">
                    <el-button size="mini" type="primary" @click="acceptGroupRequest(request)">
                      同意
                    </el-button>
                    <el-button size="mini" @click="rejectGroupRequest(request)"> 拒绝 </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 我的好友 -->
          <div class="contact-section">
            <div class="section-header">
              <div class="section-title">
                <i class="el-icon-user section-icon" />
                <span>我的好友</span>
                <span class="friend-count">({{ userFriends.length }})</span>
              </div>
            </div>
            <div class="section-content">
              <div v-if="userFriends.length === 0" class="empty-state">
                <i class="el-icon-user-solid" />
                <span>还没有好友，快去添加一些好友吧</span>
              </div>
              <div
                v-for="friend in userFriends"
                :key="friend.id"
                class="friend-item compact"
                @click="selectFriend(friend)"
                @contextmenu="handleRightClick(friend, $event)"
              >
                <el-avatar :src="friend.avatar" size="medium">
                  {{ getDisplayName(friend).charAt(0) }}
                </el-avatar>
                <div class="friend-info">
                  <div class="friend-name">
                    {{ getDisplayName(friend) }}
                    <span v-if="friendRemarks[friend.id]" class="real-name-small">
                      ({{ friend.name }})
                    </span>
                  </div>
                  <div class="friend-status">
                    <span :class="['status-dot', { online: friend.online }]" />
                    {{ friend.online ? '在线' : '离线' }}
                  </div>
                </div>
                <div class="friend-actions">
                  <el-button
                    type="text"
                    size="small"
                    icon="el-icon-chat-dot-square"
                    @click.stop="selectFriend(friend)"
                  >
                    聊天
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 我的群聊 -->
          <div class="contact-section">
            <div class="section-header">
              <div class="section-title">
                <i class="el-icon-s-custom section-icon" />
                <span>我的群聊</span>
                <span class="friend-count">({{ userGroups.length }})</span>
              </div>
            </div>
            <div class="section-content">
              <div v-if="userGroups.length === 0" class="empty-state">
                <i class="el-icon-chat-dot-square" />
                <span>还没有加入群聊</span>
              </div>
              <div
                v-for="group in userGroups"
                :key="group.id"
                class="friend-item compact"
                @click="selectFriend(group)"
                @contextmenu="handleRightClick(group, $event)"
              >
                <el-avatar :src="group.avatar" size="medium">
                  {{ getDisplayName(group).charAt(0) }}
                </el-avatar>
                <div class="friend-info">
                  <div class="friend-name">
                    {{ getDisplayName(group) }}
                    <span v-if="groupRemarks[group.id]" class="real-name-small">
                      ({{ group.name }})
                    </span>
                    <el-tag size="mini" type="info"> 群 </el-tag>
                  </div>
                  <div class="group-info">
                    <i class="el-icon-user" />
                    {{ group.memberCount }}人
                    <span class="user-role">· {{ getCurrentUserRole(group) }}</span>
                  </div>
                </div>
                <div class="friend-actions">
                  <el-button
                    type="text"
                    size="small"
                    icon="el-icon-chat-dot-square"
                    @click.stop="selectFriend(group)"
                  >
                    聊天
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 聊天区域 -->
    <div class="chat-area">
      <div v-if="!currentFriend" class="no-chat">
        <i class="el-icon-chat-dot-round" />
        <p>选择一个好友开始聊天</p>
      </div>

      <div v-else class="chat-content">
        <!-- 聊天头部 -->
        <!-- 聊天区域头部 -->
        <div class="chat-header">
          <div
            class="chat-header-info"
            @click="
              currentFriend.type === 'group'
                ? showGroupInfo(currentFriend)
                : showFriendInfo(currentFriend)
            "
          >
            <el-avatar :src="currentFriend.avatar">
              {{ getDisplayName(currentFriend).charAt(0) }}
            </el-avatar>
            <div class="header-text">
              <div class="chat-title">
                {{ getDisplayName(currentFriend) }}
                <span
                  v-if="groupRemarks[currentFriend.id] && currentFriend.type === 'group'"
                  class="real-name-hint"
                >
                  ({{ currentFriend.name }})
                </span>
                <el-tag v-if="currentFriend.type === 'group'" size="mini" type="info">
                  群聊
                </el-tag>
                <span v-else-if="currentFriend.online" class="online-dot" />
              </div>
              <div class="chat-subtitle">
                <span v-if="currentFriend.type === 'group'">
                  {{ currentFriend.memberCount }}人
                </span>
                <span v-else>
                  {{ currentFriend.online ? '在线' : '离线' }}
                </span>
              </div>
            </div>
          </div>
          <div class="chat-header-actions">
            <el-button
              v-if="currentFriend.type === 'group'"
              type="text"
              icon="el-icon-user"
              @click="showGroupMembers(currentFriend)"
            >
              成员
            </el-button>
            <el-button
              type="text"
              icon="el-icon-more"
              @click="
                currentFriend.type === 'group'
                  ? showGroupInfo(currentFriend)
                  : showFriendInfo(currentFriend)
              "
            >
              详情
            </el-button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div ref="messagesContainer" class="messages-container">
          <div
            v-for="message in currentMessages"
            :key="message.id"
            class="message-item"
            :class="{
              'own-message': message.isOwn && message.type !== 'system',
              'system-message-container': message.type === 'system',
            }"
          >
            <!-- 系统消息 -->
            <div v-if="message.type === 'system'" class="system-message">
              {{ message.content }}
              <div class="message-time">
                {{ formatTime(message.timestamp) }}
              </div>
            </div>
            <!-- 普通消息 -->
            <template v-else>
              <el-avatar
                :src="
                  message.isOwn ? userInfo.avatar : message.senderAvatar || currentFriend.avatar
                "
                size="small"
                class="message-avatar"
              >
                {{
                  message.isOwn
                    ? userInfo.name.charAt(0)
                    : message.senderName
                      ? message.senderName.charAt(0)
                      : currentFriend.name.charAt(0)
                }}
              </el-avatar>
              <div class="message-content">
                <div v-if="currentFriend.type === 'group' && !message.isOwn" class="sender-name">
                  {{ message.senderName }}
                </div>
                <div class="message-bubble">
                  <div v-if="message.type === 'text'" class="text-message">
                    {{ message.content }}
                  </div>
                  <div v-else-if="message.type === 'image'" class="image-message">
                    <img :src="message.content" alt="图片" @click="previewImage(message.content)" />
                  </div>
                  <!-- 修改文件消息的显示结构 -->
                  <div
                    v-else-if="message.type === 'file'"
                    class="file-message"
                    :class="{ 'media-file': message.isImage || message.isVideo }"
                  >
                    <!-- 图片预览 -->
                    <div v-if="message.isImage && !message.isLoading" class="image-preview">
                      <img
                        :src="message.fileUrl"
                        :alt="message.fileName"
                        @click="previewImage(message.fileUrl)"
                      />
                    </div>

                    <!-- 视频播放 -->
                    <div v-else-if="message.isVideo && !message.isLoading" class="video-preview">
                      <video controls :src="message.fileUrl" preload="metadata" />
                    </div>

                    <!-- 普通文件或加载中 -->
                    <template v-else>
                      <div class="file-icon" :class="{ loading: message.isLoading }">
                        <i v-if="!message.isLoading" :class="getFileIcon(message.fileName)" />
                        <i v-else class="el-icon-loading" />
                      </div>
                      <div class="file-info">
                        <div class="file-name" :title="message.fileName">
                          {{ message.fileName }}
                        </div>
                        <div class="file-size">
                          {{ formatFileSize(message.fileSize) }}
                        </div>
                      </div>
                      <el-button
                        v-if="!message.isLoading"
                        type="text"
                        size="mini"
                        class="file-download"
                        @click="downloadFile(message)"
                      >
                        <i class="el-icon-download" />
                      </el-button>
                    </template>
                  </div>
                  <div v-else-if="message.type === 'system'" class="system-message">
                    {{ message.content }}
                  </div>
                </div>
                <div class="message-time">
                  {{ formatTime(message.timestamp) }}
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <!-- 工具栏 -->
          <div class="input-toolbar">
            <el-button ref="emojiButton" type="text" size="small" @click="toggleEmojiPanel">
              <i class="el-icon-thumb" style="font-size: 16px" />
            </el-button>

            <input
              ref="imageInput"
              type="file"
              accept="image/*"
              style="display: none"
              @change="handleImageUpload"
            />
            <el-button type="text" size="small" @click="$refs.imageInput.click()">
              <i class="el-icon-picture-outline" style="font-size: 16px" />
            </el-button>
            <input ref="fileInput" type="file" style="display: none" @change="handleFileUpload" />
            <el-button type="text" size="small" @click="$refs.fileInput.click()">
              <i class="el-icon-document" style="font-size: 16px" />
            </el-button>
          </div>

          <!-- 表情包面板 -->
          <div
            v-show="showEmojiPanel"
            ref="emojiPanel"
            class="emoji-panel"
            :style="{ left: emojiPanelPosition + 'px' }"
          >
            <!-- 表情分类标签 -->
            <div class="emoji-categories">
              <div
                v-if="recentEmojis.length > 0"
                :class="['category-tab', { active: activeEmojiCategory === 'recent' }]"
                @click="selectEmojiCategory('recent')"
              >
                最近
              </div>
              <div
                v-for="category in emojiCategories"
                :key="category.key"
                :class="['category-tab', { active: activeEmojiCategory === category.key }]"
                @click="selectEmojiCategory(category.key)"
              >
                {{ category.name }}
              </div>
            </div>

            <!-- 表情内容区域 -->
            <div class="emoji-content">
              <!-- 最近使用的表情 -->
              <div
                v-if="activeEmojiCategory === 'recent' && recentEmojis.length > 0"
                class="emoji-grid"
              >
                <div
                  v-for="(emoji, index) in recentEmojis"
                  :key="`recent-${index}`"
                  class="emoji-item"
                  @click="insertEmoji(emoji)"
                >
                  {{ emoji }}
                </div>
              </div>

              <!-- 分类表情 -->
              <div
                v-for="category in emojiCategories"
                v-show="activeEmojiCategory === category.key"
                :key="category.key"
                class="emoji-grid"
              >
                <div
                  v-for="(emoji, emojiIndex) in category.emojis"
                  :key="`${category.key}-${emojiIndex}`"
                  class="emoji-item"
                  @click="insertEmoji(emoji)"
                >
                  {{ emoji }}
                </div>
              </div>
            </div>
          </div>

          <!-- 消息输入框和发送按钮 -->
          <div class="message-input-container">
            <el-input
              ref="messageInput"
              v-model="messageInput"
              type="textarea"
              :rows="3"
              placeholder="输入消息..."
              resize="none"
              @keyup.enter.native="sendMessage"
            />
            <el-button type="primary" :disabled="!messageInput.trim()" @click="sendMessage">
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加好友对话框 -->
    <el-dialog title="添加好友" :visible.sync="showAddFriendDialog" width="400px">
      <el-form ref="addFriendForm" :model="addFriendForm" :rules="addFriendRules">
        <el-form-item label="好友ID/手机号" prop="friendId">
          <el-input v-model="addFriendForm.friendId" placeholder="请输入好友ID或手机号" />
        </el-form-item>
        <el-form-item label="验证信息" prop="message">
          <el-input
            v-model="addFriendForm.message"
            type="textarea"
            :rows="3"
            placeholder="请输入验证信息"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAddFriendDialog = false">取消</el-button>
        <el-button type="primary" @click="sendFriendRequest">发送申请</el-button>
      </span>
    </el-dialog>

    <!-- 创建群聊对话框 -->
    <el-dialog title="创建群聊" :visible.sync="showCreateGroupDialog" width="500px">
      <el-form ref="createGroupForm" :model="createGroupForm" :rules="createGroupRules">
        <el-form-item label="群名称" prop="groupName">
          <el-input v-model="createGroupForm.groupName" placeholder="请输入群名称" />
        </el-form-item>
        <el-form-item label="群描述" prop="groupDesc">
          <el-input
            v-model="createGroupForm.groupDesc"
            type="textarea"
            :rows="3"
            placeholder="请输入群描述"
          />
        </el-form-item>
        <el-form-item label="邀请好友">
          <el-checkbox-group v-model="createGroupForm.selectedFriends">
            <el-checkbox
              v-for="friend in availableFriends"
              :key="friend.id"
              :label="friend.id"
              class="friend-checkbox"
            >
              <el-avatar :src="friend.avatar" size="small">
                {{ friend.name.charAt(0) }}
              </el-avatar>
              {{ friend.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showCreateGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="createGroup">创建群聊</el-button>
      </span>
    </el-dialog>

    <!-- 加入群聊对话框 -->
    <el-dialog title="加入群聊" :visible.sync="showJoinGroupDialog" width="400px">
      <el-form ref="joinGroupForm" :model="joinGroupForm" :rules="joinGroupRules">
        <el-form-item label="群ID/群名称" prop="groupId">
          <el-input v-model="joinGroupForm.groupId" placeholder="请输入群ID或群名称" />
        </el-form-item>
        <el-form-item label="申请理由" prop="reason">
          <el-input
            v-model="joinGroupForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入申请理由"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showJoinGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="sendJoinGroupRequest">发送申请</el-button>
      </span>
    </el-dialog>

    <!-- 修改群成员对话框，使用selectedGroupForMembers而不是currentFriend -->
    <el-dialog title="群成员" :visible.sync="showGroupMembersDialog" width="500px">
      <div
        v-if="selectedGroupForMembers && selectedGroupForMembers.type === 'group'"
        class="group-members"
      >
        <div v-for="member in selectedGroupForMembers.members" :key="member.id" class="member-item">
          <el-avatar :src="member.avatar">
            {{ member.name.charAt(0) }}
          </el-avatar>
          <div class="member-info">
            <div class="member-name">
              {{ getMemberDisplayName(member) }}
            </div>
            <div v-if="getMemberRemark(member)" class="member-real-name">
              {{ member.name }}
            </div>
            <div class="member-role">
              {{ member.role === 'owner' ? '群主' : member.role === 'admin' ? '管理员' : '成员' }}
            </div>
          </div>
          <div
            v-if="isGroupOwner(selectedGroupForMembers) && member.id !== userInfo.id"
            class="member-actions"
          >
            <el-button
              type="danger"
              size="mini"
              @click="removeMember(member, selectedGroupForMembers)"
            >
              <i class="el-icon-delete"></i> 移除
            </el-button>
          </div>
        </div>
      </div>
      <div v-else class="no-members">
        <i class="el-icon-warning"></i>
        <p>无法获取群成员信息</p>
      </div>
    </el-dialog>

    <!-- 图片上传隐藏input -->
    <input
      ref="imageInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleImageUpload"
    />

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="imagePreviewVisible" width="50%" :show-close="true">
      <img :src="previewImageUrl" style="width: 100%" alt="预览图片" />
    </el-dialog>

    <!-- 在模板的最后，添加以下对话框 -->

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
    >
      <div class="context-menu-item" @click="showInfo(contextMenuTarget)">
        <i class="el-icon-info" />
        {{ contextMenuTarget?.type === 'group' ? '群信息' : '好友信息' }}
      </div>
      <div
        v-if="contextMenuTarget?.type === 'group'"
        class="context-menu-item"
        @click="showGroupMembers(contextMenuTarget)"
      >
        <i class="el-icon-user" />
        查看成员
      </div>
      <div
        v-if="contextMenuTarget?.type !== 'group'"
        class="context-menu-item"
        @click="deleteFriend(contextMenuTarget)"
      >
        <i class="el-icon-delete" />
        删除好友
      </div>
      <div v-else class="context-menu-item" @click="leaveGroup(contextMenuTarget)">
        <i class="el-icon-remove" />
        {{ isGroupOwner(contextMenuTarget) ? '解散群聊' : '退出群聊' }}
      </div>
    </div>

    <!-- 好友信息对话框 -->
    <el-dialog
      title="好友信息"
      :visible.sync="showFriendInfoDialog"
      width="450px"
      class="friend-info-dialog"
    >
      <div v-if="friendInfoData" class="friend-info-content">
        <div class="friend-header">
          <el-avatar :src="friendInfoData.avatar" size="large">
            {{ friendInfoData.name.charAt(0) }}
          </el-avatar>
          <div class="friend-details">
            <h3>{{ getDisplayName(friendInfoData) }}</h3>
            <p v-if="friendRemarks[friendInfoData.id]" class="real-name">
              用户名：{{ friendInfoData.name }}
            </p>
            <p class="online-status">
              <span :class="['status-dot', { online: friendInfoData.online }]" />
              {{ friendInfoData.online ? '在线' : '离线' }}
            </p>
          </div>
        </div>

        <div class="friend-extra-info">
          <div class="info-item">
            <label>用户ID：</label>
            <span class="user-id">{{ friendInfoData.userId || friendInfoData.id }}</span>
            <el-button
              type="text"
              size="mini"
              @click="copyUserId(friendInfoData.userId || friendInfoData.id)"
            >
              <i class="el-icon-copy-document" /> 复制
            </el-button>
          </div>

          <div class="info-item">
            <label>用户名：</label>
            <span>{{ friendInfoData.name }}</span>
          </div>

          <div class="info-item">
            <label>备注名：</label>
            <div v-if="!editingRemark" class="remark-section">
              <span class="remark-display">{{ friendRemarks[friendInfoData.id] || '未设置' }}</span>
              <el-button type="text" size="mini" @click="editRemark">
                <i class="el-icon-edit" /> 编辑
              </el-button>
            </div>
            <div v-else class="remark-section">
              <el-input
                ref="remarkInput"
                v-model="tempRemark"
                size="small"
                placeholder="请输入备注名"
                class="remark-input"
                @keyup.enter.native="saveRemark"
                @keyup.esc.native="cancelEditRemark"
              />
              <div class="remark-actions">
                <el-button type="primary" size="mini" @click="saveRemark">
                  <i class="el-icon-check" />
                </el-button>
                <el-button size="mini" @click="cancelEditRemark">
                  <i class="el-icon-close" />
                </el-button>
              </div>
            </div>
          </div>

          <div class="info-item">
            <label>个性签名：</label>
            <span>{{ friendInfoData.signature || '这个人很懒，什么都没留下' }}</span>
          </div>

          <div class="info-item">
            <label>添加时间：</label>
            <span>{{ formatTime(friendInfoData.addTime || Date.now()) }}</span>
          </div>
        </div>

        <div class="friend-actions">
          <el-button type="danger" size="small" @click="deleteFriend(friendInfoData)">
            <i class="el-icon-delete" /> 删除好友
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 群信息对话框 -->
    <el-dialog
      title="群聊信息"
      :visible.sync="showGroupInfoDialog"
      width="450px"
      class="group-info-dialog"
    >
      <div v-if="groupInfoData" class="group-info-content">
        <div class="group-header">
          <el-avatar :src="groupInfoData.avatar" size="large">
            {{ groupInfoData.name.charAt(0) }}
          </el-avatar>
          <div class="group-details">
            <h3>{{ getDisplayName(groupInfoData) }}</h3>
            <p v-if="groupRemarks[groupInfoData.id]" class="real-name">
              群名称：{{ groupInfoData.name }}
            </p>
            <p class="group-status">
              <i class="el-icon-user" />
              {{ groupInfoData.memberCount }}人
            </p>
          </div>
        </div>

        <div class="group-extra-info">
          <div class="info-item">
            <label>群ID：</label>
            <span class="group-id">{{ groupInfoData.userId || groupInfoData.id }}</span>
            <el-button
              type="text"
              size="mini"
              @click="copyUserId(groupInfoData.userId || groupInfoData.id)"
            >
              <i class="el-icon-copy-document" /> 复制
            </el-button>
          </div>

          <div class="info-item">
            <label>群名称：</label>
            <div v-if="!editingGroupName" class="name-section">
              <span class="name-display">{{ groupInfoData.name }}</span>
              <el-button
                v-if="isGroupOwner(groupInfoData)"
                type="text"
                size="mini"
                @click="editGroupName"
              >
                <i class="el-icon-edit" /> 编辑
              </el-button>
              <span v-else class="permission-hint">只有群主可以修改</span>
            </div>
            <div v-else class="name-section">
              <el-input
                ref="groupNameInput"
                v-model="tempGroupName"
                size="small"
                placeholder="请输入群名称"
                class="name-input"
                :maxlength="20"
                show-word-limit
                @keyup.enter.native="saveGroupName"
                @keyup.esc.native="cancelEditGroupName"
              />
              <div class="name-actions">
                <el-button type="primary" size="mini" @click="saveGroupName">
                  <i class="el-icon-check" />
                </el-button>
                <el-button size="mini" @click="cancelEditGroupName">
                  <i class="el-icon-close" />
                </el-button>
              </div>
            </div>
          </div>

          <div class="info-item">
            <label>我的备注：</label>
            <div v-if="!editingGroupRemark" class="remark-section">
              <span class="remark-display">{{ groupRemarks[groupInfoData.id] || '未设置' }}</span>
              <el-button type="text" size="mini" @click="editGroupRemark">
                <i class="el-icon-edit" /> 编辑
              </el-button>
            </div>
            <div v-else class="remark-section">
              <el-input
                ref="groupRemarkInput"
                v-model="tempGroupRemark"
                size="small"
                placeholder="请输入群备注（仅自己可见）"
                class="remark-input"
                :maxlength="20"
                show-word-limit
                @keyup.enter.native="saveGroupRemark"
                @keyup.esc.native="cancelEditGroupRemark"
              />
              <div class="remark-actions">
                <el-button type="primary" size="mini" @click="saveGroupRemark">
                  <i class="el-icon-check" />
                </el-button>
                <el-button size="mini" @click="cancelEditGroupRemark">
                  <i class="el-icon-close" />
                </el-button>
              </div>
            </div>
          </div>

          <div class="info-item">
            <label>我的角色：</label>
            <span>
              {{ getCurrentUserRole(groupInfoData) }}
              <el-tag v-if="isGroupOwner(groupInfoData)" size="mini" type="warning"> 群主 </el-tag>
            </span>
          </div>

          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatTime(groupInfoData.addTime || Date.now()) }}</span>
          </div>
        </div>

        <div class="group-actions">
          <el-button
            size="small"
            @click="
              contextMenuTarget
                ? showGroupMembers(contextMenuTarget)
                : showGroupMembers(currentFriend)
            "
          >
            <i class="el-icon-user" /> 查看成员
          </el-button>
          <el-button
            v-if="!isGroupOwner(groupInfoData)"
            type="danger"
            size="small"
            @click="leaveGroup(groupInfoData)"
          >
            <i class="el-icon-remove" /> 退出群聊
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { initWs, onMessage } from '@/utils/wsClient';

import {
  sendFriendRequest,
  sendGroupRequest,
  acceptFriendRequest,
  rejectFriendRequest,
  acceptGroupRequest,
  rejectGroupRequest,
  createGroup,
} from '@/api/system/chat';

export default {
  name: 'ChatApp',
  data() {
    return {
      selectedGroupForMembers: null, // 新增：存储当前查看成员的群组
      // 新增表情包相关数据
      showEmojiPanel: false, // 表情包面板显示状态
      emojiCategories: [
        {
          name: '常用',
          key: 'common',
          emojis: [
            '😀',
            '😃',
            '😄',
            '😁',
            '😆',
            '😅',
            '🤣',
            '😂',
            '🙂',
            '😊',
            '😇',
            '🥰',
            '😍',
            '😘',
            '😗',
            '😚',
            '😋',
            '😛',
            '😝',
            '😜',
            '🤪',
            '🤨',
            '🧐',
            '🤓',
            '😎',
            '🤩',
            '🥳',
            '😏',
            '😒',
            '😞',
            '😔',
            '😟',
            '😕',
            '😣',
            '😖',
            '😫',
            '😩',
            '🥺',
            '😢',
            '😭',
            '😤',
            '😠',
            '😡',
            '🤬',
            '🤯',
            '😳',
            '🥵',
            '🥶',
            '😱',
            '😨',
            '😰',
            '😥',
            '😓',
          ],
        },
        {
          name: '手势',
          key: 'gestures',
          emojis: [
            '👍',
            '👎',
            '👊',
            '✊',
            '🤛',
            '🤜',
            '🤞',
            '✌️',
            '🤟',
            '🤘',
            '👌',
            '🤏',
            '👈',
            '👉',
            '👆',
            '👇',
            '☝️',
            '✋',
            '🤚',
            '🖐️',
            '🖖',
            '👋',
            '🤙',
            '💪',
            '🦵',
            '🦶',
            '👂',
            '🦻',
            '👃',
            '🧠',
            '🦷',
            '🦴',
            '👀',
            '👁️',
            '👅',
            '👄',
            '💋',
            '🩸',
          ],
        },
        {
          name: '动物',
          key: 'animals',
          emojis: [
            '🐶',
            '🐱',
            '🐭',
            '🐹',
            '🐰',
            '🦊',
            '🐻',
            '🐼',
            '🐻‍❄️',
            '🐨',
            '🐯',
            '🦁',
            '🐮',
            '🐷',
            '🐽',
            '🐸',
            '🐵',
            '🙈',
            '🙉',
            '🙊',
            '🐒',
            '🐔',
            '🐧',
            '🐦',
            '🐤',
            '🐣',
            '🐥',
            '🦆',
            '🦅',
            '🦉',
            '🦇',
            '🐺',
            '🐗',
            '🐴',
            '🦄',
            '🐝',
            '🐛',
            '🦋',
            '🐌',
            '🐞',
            '🐜',
            '🦟',
            '🦗',
            '🕷️',
            '🦂',
            '🐢',
            '🐍',
            '🦎',
            '🦖',
            '🦕',
            '🐙',
            '🦑',
            '🦐',
            '🦞',
            '🦀',
            '🐡',
            '🐠',
            '🐟',
            '🐬',
            '🐳',
            '🐋',
            '🦈',
            '🐊',
            '🐅',
            '🐆',
            '🦓',
            '🦍',
            '🦧',
            '🐘',
            '🦛',
            '🦏',
            '🐪',
            '🐫',
            '🦒',
            '🦘',
            '🐃',
            '🐂',
            '🐄',
            '🐎',
            '🐖',
            '🐏',
            '🐑',
            '🦙',
            '🐐',
            '🦌',
            '🐕',
            '🐩',
            '🦮',
            '🐕‍🦺',
            '🐈',
            '🐈‍⬛',
            '🐓',
            '🦃',
            '🦚',
            '🦜',
            '🦢',
            '🦩',
            '🕊️',
            '🐇',
            '🦝',
            '🦨',
            '🦡',
            '🦦',
            '🦥',
            '🐁',
            '🐀',
            '🐿️',
            '🦔',
          ],
        },
        {
          name: '物品',
          key: 'objects',
          emojis: [
            '⌚',
            '📱',
            '📲',
            '💻',
            '⌨️',
            '🖥️',
            '🖨️',
            '🖱️',
            '🖲️',
            '🕹️',
            '🗜️',
            '💽',
            '💾',
            '💿',
            '📀',
            '📼',
            '📷',
            '📸',
            '📹',
            '🎥',
            '📽️',
            '🎞️',
            '📞',
            '☎️',
            '📟',
            '📠',
            '📺',
            '📻',
            '🎙️',
            '🎚️',
            '🎛️',
            '⏱️',
            '⏲️',
            '⏰',
            '🕰️',
            '⌛',
            '⏳',
            '📡',
            '🔋',
            '🔌',
            '💡',
            '🔦',
            '🕯️',
            '🪔',
            '🧯',
            '🛢️',
            '💸',
            '💵',
            '💴',
            '💶',
            '💷',
            '💰',
            '💳',
            '💎',
            '⚖️',
            '🦯',
            '🧰',
            '🔧',
            '🔨',
            '⚒️',
            '🛠️',
            '⛏️',
            '🔩',
            '⚙️',
            '🧱',
            '⛓️',
            '🧲',
            '🔫',
            '💣',
            '🧨',
            '🪓',
            '🔪',
            '🗡️',
            '⚔️',
            '🛡️',
            '🚬',
            '⚰️',
            '⚱️',
            '🏺',
            '🔮',
            '📿',
            '🧿',
            '💈',
            '⚗️',
            '🔭',
            '🔬',
            '🕳️',
            '🩹',
            '🩺',
            '💊',
            '💉',
            '🧬',
            '🦠',
            '🧫',
            '🧪',
            '🌡️',
            '🧹',
            '🪣',
            '🧽',
            '🧴',
            '🧷',
            '🧼',
            '🪒',
            '🧾',
            '🧻',
            '🪑',
            '🚽',
            '🚿',
            '🛁',
            '🪤',
            '🧸',
            '🪆',
            '🖼️',
            '🪞',
            '🪟',
            '🛏️',
            '🛋️',
            '🪑',
            '🚪',
            '🪜',
            '🪃',
            '🏹',
            '🎣',
            '🤿',
            '🥽',
            '🥼',
            '🦺',
            '👑',
            '📿',
            '💍',
            '💎',
            '🔇',
            '🔈',
            '🔉',
            '🔊',
            '📢',
            '📣',
            '📯',
            '🔔',
            '🔕',
            '🎼',
            '🎵',
            '🎶',
            '🎙️',
            '🎚️',
            '🎛️',
            '🎤',
            '🎧',
            '📻',
            '🎷',
            '🪗',
            '🎸',
            '🎹',
            '🎺',
            '🎻',
            '🪕',
            '🥁',
            '🪘',
            '📱',
            '📲',
          ],
        },
      ],
      activeEmojiCategory: 'common', // 当前激活的表情分类
      recentEmojis: [], // 最近使用的表情
      // 新增以下数据项
      showGroupInfoDialog: false,
      groupInfoData: null,
      groupRemarks: {}, // 存储群备注（每个用户独立）
      editingGroupName: false,
      editingGroupRemark: false,
      tempGroupName: '',
      tempGroupRemark: '',
      // 新增以下数据项
      showFriendInfoDialog: false,
      friendInfoData: null,
      friendRemarks: {}, // 存储好友备注名
      editingRemark: false,
      tempRemark: '',
      contextMenuVisible: false,
      contextMenuPosition: { x: 0, y: 0 },
      contextMenuTarget: null,

      activeTab: 'chat',
      searchText: '',
      currentFriend: null,
      messageInput: '',
      imagePreviewVisible: false,
      previewImageUrl: '',

      // 对话框显示状态
      showAddFriendDialog: false,
      showCreateGroupDialog: false,
      showJoinGroupDialog: false,
      showGroupMembersDialog: false,

      // 展开状态
      expandedSections: {
        friendRequests: true,
        groupRequests: true,
      },

      // 表单数据
      addFriendForm: {
        friendId: '',
        message: '我是你的好友',
      },
      createGroupForm: {
        groupName: '',
        groupDesc: '',
        selectedFriends: [],
      },
      joinGroupForm: {
        groupId: '',
        reason: '希望加入群聊',
      },

      // 验证规则
      addFriendRules: {
        friendId: [{ required: true, message: '请输入好友ID或手机号', trigger: 'blur' }],
        message: [{ required: true, message: '请输入验证信息', trigger: 'blur' }],
      },
      createGroupRules: {
        groupName: [{ required: true, message: '请输入群名称', trigger: 'blur' }],
      },
      joinGroupRules: {
        groupId: [{ required: true, message: '请输入群ID或群名称', trigger: 'blur' }],
        reason: [{ required: true, message: '请输入申请理由', trigger: 'blur' }],
      },

      // 当前用户信息
      userInfo: {
        id: this.$store.getters.id,
        name: '我',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      },

      // 在 data() 中的 friendRequests 数组中添加 userId 字段
      friendRequests: [
        // {
        //   id: 'req1',
        //   userId: 'user_zhaoliu_005',
        //   name: '赵六',
        //   avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        //   message: '我们是同事，希望能成为好友',
        //   timestamp: Date.now() - 3600000,
        // },
        // {
        //   id: 'req2',
        //   userId: 'user_sunqi_006',
        //   name: '孙七',
        //   avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
        //   message: '通过朋友推荐加你好友',
        //   timestamp: Date.now() - 7200000,
        // },
      ],

      // 群聊申请列表
      groupRequests: [
        // {
        //   id: 'greq1',
        //   applicantId: 'user123',
        //   applicantName: '李小明',
        //   applicantAvatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
        //   groupId: 'group1',
        //   groupName: '技术交流群',
        //   groupAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        //   reason: '希望学习技术知识',
        //   timestamp: Date.now() - 1800000,
        // },
      ],

      // 好友列表数据（包含群聊）
      // 在 data() 的 friends 数组中，为每个好友添加 userId 字段
      friends: [
        // {
        //   id: 'friend1',
        //   userId: 'user_zhangsan_001', // 用户ID
        //   sessionId: 'session_friend1', // 添加 sessionId
        //   name: '张三',
        //   avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        //   lastMessage: '你好，最近怎么样？',
        //   lastTime: '10:30',
        //   unreadCount: 2,
        //   online: true,
        //   type: 'user',
        //   signature: '今天天气不错~',
        //   addTime: Date.now() - 86400000,
        // },
        // {
        //   id: 'friend2',
        //   userId: 'user_lisi_002', // 用户ID
        //   sessionId: 'session_friend2', // 添加 sessionId
        //   name: '李四',
        //   avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
        //   lastMessage: '明天见面吧',
        //   lastTime: '昨天',
        //   unreadCount: 0,
        //   online: false,
        //   type: 'user',
        //   signature: '工作使我快乐',
        //   addTime: Date.now() - 172800000,
        // },
        // {
        //   id: 'group1',
        //   userId: 'group_tech_001', // 群组ID
        //   sessionId: 'session_group1', // 添加 sessionId
        //   name: '技术交流群',
        //   avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
        //   lastMessage: '王五: 大家好！',
        //   lastTime: '15:20',
        //   unreadCount: 3,
        //   online: true,
        //   type: 'group',
        //   memberCount: 8,
        //   members: [
        //     {
        //       id: 'user1',
        //       userId: 'user_me_000',
        //       name: '我',
        //       avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        //       role: 'owner',
        //     },
        //     {
        //       id: 'friend1',
        //       userId: 'user_zhangsan_001',
        //       name: '张三',
        //       avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        //       role: 'admin',
        //     },
        //     {
        //       id: 'friend2',
        //       userId: 'user_lisi_002',
        //       name: '李四',
        //       avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
        //       role: 'member',
        //     },
        //     {
        //       id: 'friend3',
        //       userId: 'user_wangwu_003',
        //       name: '王五',
        //       avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
        //       role: 'member',
        //     },
        //     {
        //       id: 'friend4',
        //       userId: 'user_zhaoliu_004',
        //       name: '赵六',
        //       avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        //       role: 'member',
        //     },
        //   ],
        // },
      ],

      // 聊天消息数据
      messages: {
        // session_friend1: [
        //   {
        //     id: 1,
        //     content: '你好！',
        //     timestamp: Date.now() - 3600000,
        //     isOwn: false,
        //     type: 'text',
        //   },
        //   {
        //     id: 2,
        //     content: '你好，最近怎么样？',
        //     timestamp: Date.now() - 1800000,
        //     isOwn: true,
        //     type: 'text',
        //   },
        // ],
        // session_friend2: [
        //   {
        //     id: 1,
        //     content: '明天的会议准备好了吗？',
        //     timestamp: Date.now() - 86400000,
        //     isOwn: true,
        //     type: 'text',
        //   },
        //   {
        //     id: 2,
        //     content: '明天见面吧',
        //     timestamp: Date.now() - 86400000 + 300000,
        //     isOwn: false,
        //     type: 'text',
        //   },
        // ],
        // session_group1: [
        //   {
        //     id: 1,
        //     content: '欢迎大家加入技术交流群！',
        //     timestamp: Date.now() - 7200000,
        //     isOwn: true,
        //     type: 'system',
        //   },
        //   {
        //     id: 2,
        //     content: '大家好，我是新人',
        //     timestamp: Date.now() - 3600000,
        //     isOwn: false,
        //     type: 'text',
        //     senderId: 'friend1',
        //     senderName: '张三',
        //     senderAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        //   },
        //   {
        //     id: 3,
        //     content: '欢迎欢迎！',
        //     timestamp: Date.now() - 3500000,
        //     isOwn: true,
        //     type: 'text',
        //   },
        //   {
        //     id: 4,
        //     content: '大家好！',
        //     timestamp: Date.now() - 1800000,
        //     isOwn: false,
        //     type: 'text',
        //     senderId: 'friend3',
        //     senderName: '王五',
        //     senderAvatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
        //   },
        // ],
      },
      // 表情面板位置
      emojiPanelPosition: 0,
    };
  },

  computed: {
    // 用户好友列表（排除群聊）
    userFriends() {
      return this.friends.filter((friend) => friend && friend.type !== 'group');
    },

    // 用户群聊列表
    userGroups() {
      return this.friends.filter((friend) => friend && friend.type === 'group');
    },
    // 过滤好友列表
    filteredFriends() {
      if (!this.searchText) {
        return this.friends.filter((friend) => friend != null);
      }
      return this.friends.filter((friend) => {
        if (!friend) return false;
        const displayName = this.getDisplayName(friend);
        return displayName.toLowerCase().includes(this.searchText.toLowerCase());
      });
    },

    currentMessages() {
      if (!this.currentFriend) return [];
      return this.messages[this.currentFriend.sessionId] || [];
    },

    availableFriends() {
      return this.friends.filter((friend) => friend.type === 'user');
    },

    // 待处理申请数量
    pendingRequestsCount() {
      return (this.friendRequests?.length || 0) + (this.groupRequests?.length || 0);
    },
  },

  mounted() {
    initWs(this.$store.getters.token);
    onMessage(this.handleWsMessage);
    document.addEventListener('click', this.handleClickOutside);
    // 模拟接收新的好友申请
    // setInterval(() => {
    //   if (Math.random() < 0.1) {
    //     // 10% 概率
    //     const names = ['小明', '小红', '小刚', '小美', '小华'];
    //     const randomName = names[Math.floor(Math.random() * names.length)];
    //     const newRequest = {
    //       id: 'req' + Date.now(),
    //       name: randomName,
    //       avatar: `https://cube.elemecdn.com/${Math.floor(
    //         Math.random() * 10
    //       )}/88/03b0d39583f48206768a7534e55bcpng.png`,
    //       message: '希望能成为好友',
    //       timestamp: Date.now(),
    //     };
    //     this.friendRequests.push(newRequest);
    //   }
    // }, 30000); // 每30秒检查一次
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleWsMessage(data) {
      const jsonData = JSON.parse(data);
      if (jsonData.messageType === '4') {
        jsonData.friendRequests.forEach((friend) => {
          // this.friendRequests.push(friend);
          this.friendRequests.splice(0, 0, friend);
        });
      } else if (jsonData.messageType === '14') {
        jsonData.groupRequests.forEach((group) => {
          this.groupRequests.splice(0, 0, group);
        });
      } else if (jsonData.messageType === '1') {
        //同意好友申请
        this.friends.splice(0, 0, jsonData.friends[0]);
        Object.assign(this.messages, jsonData.messages);
      } else if (jsonData.messageType === '001') {
        //拒绝或者同意好友后，如果也添加了对方为好友的申请，那么要把对方的好友申请删除
        const index = this.friendRequests.findIndex((req) => req.id === jsonData.messageContent);
        if (index > -1) {
          this.friendRequests.splice(index, 1);
        }
      } else if (jsonData.messageType === '09') {
        //同意群聊申请后给群聊发通知
        const index = this.friends.findIndex((req) => req.id === jsonData.friends[0].id);
        if (index > -1) {
          this.friends[index].lastMessage = jsonData.friends[0].lastMessage;
          this.friends[index].lastTime = jsonData.friends[0].lastTime;
          this.friends[index].memberCount = jsonData.friends[0].memberCount;
          this.friends[index].members.push(jsonData.friends[0].members[0]);
          Object.keys(jsonData.messages).forEach((sessionKey) => {
            if (this.messages[sessionKey]) {
              this.messages[sessionKey] = [
                ...this.messages[sessionKey],
                ...jsonData.messages[sessionKey],
              ];
            } else {
              this.messages[sessionKey] = jsonData.messages[sessionKey];
            }
          });
        }
      } else if (jsonData.messageType === '9') {
        this.friends.splice(0, 0, jsonData.friends[0]);
        Object.keys(jsonData.messages).forEach((sessionKey) => {
          this.messages[sessionKey] = jsonData.messages[sessionKey];
        });
      } else if (jsonData.messageType === '3') {
        this.friends.splice(0, 0, jsonData.friends[0]);
      } else {
        // 初始化
        this.friends = jsonData.friends;
        this.messages = jsonData.messages;
        this.friendRequests = jsonData.friendRequests;
        this.groupRequests = jsonData.groupRequests;
      }
    },
    // 修改右键菜单的处理方法，添加查看群成员的选项
    handleContextMenu(event, item) {
      event.preventDefault();
      this.contextMenuTarget = item;
      this.contextMenuPosition = {
        x: event.clientX,
        y: event.clientY,
      };
      this.contextMenuVisible = true;
    },
    // 修改查看群成员的方法，使用contextMenuTarget而不是currentFriend
    showGroupMembers(group) {
      // 确保使用右键点击的群组而不是当前选中的聊天
      this.selectedGroupForMembers = group;
      this.showGroupMembersDialog = true;
      this.hideContextMenu();
    },
    // 修改移除成员方法，接收群组参数
    removeMember(member, group) {
      if (!this.isGroupOwner(group)) {
        this.$message.warning('只有群主可以移除成员');
        return;
      }

      this.$confirm(`确定要将 ${this.getMemberDisplayName(member)} 移出群聊吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 使用传入的群组而不是currentFriend
          const targetGroup = this.friends.find((f) => f.id === group.id);
          if (targetGroup && targetGroup.type === 'group') {
            // 从成员列表中移除
            const memberIndex = targetGroup.members.findIndex((m) => m.id === member.id);
            if (memberIndex > -1) {
              targetGroup.members.splice(memberIndex, 1);
              targetGroup.memberCount = targetGroup.members.length;

              // 同步更新当前显示的群聊成员
              if (this.selectedGroupForMembers && this.selectedGroupForMembers.id === group.id) {
                const currentMemberIndex = this.selectedGroupForMembers.members.findIndex(
                  (m) => m.id === member.id
                );
                if (currentMemberIndex > -1) {
                  this.selectedGroupForMembers.members.splice(currentMemberIndex, 1);
                  this.selectedGroupForMembers.memberCount =
                    this.selectedGroupForMembers.members.length;
                }
              }

              // 添加系统消息
              if (!this.messages[targetGroup.id]) {
                this.$set(this.messages, targetGroup.id, []);
              }
              this.messages[targetGroup.id].push({
                id: Date.now(),
                content: `${this.getMemberDisplayName(member)} 已被移出群聊`,
                timestamp: Date.now(),
                isOwn: false,
                type: 'system',
              });

              this.$message.success(`已将 ${this.getMemberDisplayName(member)} 移出群聊`);
            }
          }
        })
        .catch(() => {
          // 取消操作
        });
    },
    // 修改 handleFileUpload 方法
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file || !this.currentFriend) return;

      // 判断文件类型
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');

      // 创建临时消息对象，添加 loading 状态
      const tempMessage = {
        id: Date.now(),
        type: 'file',
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        isImage: isImage,
        isVideo: isVideo,
        isLoading: true,
        timestamp: Date.now(),
        isOwn: true,
      };

      // 添加到消息列表
      if (!this.messages[this.currentFriend.sessionId]) {
        this.$set(this.messages, this.currentFriend.sessionId, []);
      }

      this.messages[this.currentFriend.sessionId].push(tempMessage);

      // 更新最后一条消息预览
      this.currentFriend.lastMessage = this.getLastMessagePreview('', 'file', file.name);
      this.currentFriend.lastTime = this.formatTime(tempMessage.timestamp);

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 如果是图片或视频，创建预览URL
      if (isImage || isVideo) {
        const fileUrl = URL.createObjectURL(file);

        // 模拟上传过程
        setTimeout(() => {
          // 上传完成后更新消息对象
          const index = this.messages[this.currentFriend.sessionId].findIndex(
            (m) => m.id === tempMessage.id
          );
          if (index !== -1) {
            // 更新消息，添加文件URL并移除loading状态
            this.$set(this.messages[this.currentFriend.sessionId], index, {
              ...tempMessage,
              isLoading: false,
              fileUrl: fileUrl,
            });
          }

          // 显示上传成功提示
          this.$message({
            message: '文件发送成功',
            type: 'success',
          });

          // 清空input，允许选择相同的文件
          event.target.value = '';

          // 模拟回复
          setTimeout(
            () => {
              this.simulateReply();
            },
            1000 + Math.random() * 2000
          );
        }, 1500); // 模拟上传时间
      } else {
        // 普通文件处理
        setTimeout(() => {
          const index = this.messages[this.currentFriend.sessionId].findIndex(
            (m) => m.id === tempMessage.id
          );
          if (index !== -1) {
            this.$set(this.messages[this.currentFriend.sessionId], index, {
              ...tempMessage,
              isLoading: false,
              fileUrl: URL.createObjectURL(file),
            });
          }

          this.$message({
            message: '文件发送成功',
            type: 'success',
          });

          event.target.value = '';

          setTimeout(
            () => {
              this.simulateReply();
            },
            1000 + Math.random() * 2000
          );
        }, 1500);
      }
    },

    // 获取文件图标
    getFileIcon(fileName) {
      if (!fileName) return 'el-icon-document';

      const extension = fileName.split('.').pop().toLowerCase();

      // 根据文件扩展名返回不同的图标
      const iconMap = {
        // 文档
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        pdf: 'el-icon-document',
        txt: 'el-icon-document',

        // 表格
        xls: 'el-icon-s-grid',
        xlsx: 'el-icon-s-grid',
        csv: 'el-icon-s-grid',

        // 演示文稿
        ppt: 'el-icon-data-board',
        pptx: 'el-icon-data-board',

        // 压缩文件
        zip: 'el-icon-folder',
        rar: 'el-icon-folder',
        '7z': 'el-icon-folder',

        // 音频
        mp3: 'el-icon-headset',
        wav: 'el-icon-headset',
        ogg: 'el-icon-headset',

        // 视频
        mp4: 'el-icon-video-camera',
        avi: 'el-icon-video-camera',
        mov: 'el-icon-video-camera',
        mkv: 'el-icon-video-camera',
        webm: 'el-icon-video-camera',

        // 图片
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        webp: 'el-icon-picture',

        // 代码
        js: 'el-icon-s-opportunity',
        html: 'el-icon-s-opportunity',
        css: 'el-icon-s-opportunity',
        java: 'el-icon-s-opportunity',
        py: 'el-icon-s-opportunity',
      };

      return iconMap[extension] || 'el-icon-document';
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';

      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 下载文件
    downloadFile(message) {
      if (!message.fileUrl) {
        this.$message.error('文件链接不可用');
        return;
      }

      // 显示下载中的加载状态
      const loadingInstance = this.$loading({
        lock: true,
        text: '文件下载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      // 在实际应用中，这里应该调用API从服务器下载文件
      // 这里我们使用setTimeout模拟下载过程
      setTimeout(() => {
        try {
          // 创建下载链接
          const a = document.createElement('a');
          a.href = message.fileUrl;
          a.download = message.fileName || 'download';
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);

          // 显示下载成功提示
          this.$message({
            message: '文件下载成功',
            type: 'success',
          });
        } catch (error) {
          console.error('下载文件失败:', error);
          this.$message.error('下载文件失败，请重试');
        } finally {
          // 关闭加载状态
          loadingInstance.close();
        }
      }, 1500); // 模拟下载时间
    },

    // 修改 getLastMessagePreview 方法以支持文件消息
    getLastMessagePreview(content, type, fileName) {
      // 如果是文件类型，显示文件名
      if (type === 'file') {
        return `[文件] ${
          fileName && fileName.length > 15 ? fileName.substring(0, 15) + '...' : fileName || '文件'
        }`;
      }

      // 如果是图片类型
      if (type === 'image') {
        return '[图片]';
      }

      // 如果内容只包含表情，显示表情
      const emojiRegex =
        /^[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]+$/u;
      if (content && emojiRegex.test(content)) {
        return content;
      }

      // 限制预览长度
      return content && content.length > 20 ? content.substring(0, 20) + '...' : content || '';
    },
    // 修改切换表情面板方法
    toggleEmojiPanel() {
      this.showEmojiPanel = !this.showEmojiPanel;

      if (this.showEmojiPanel) {
        // 计算表情面板的位置，使其显示在表情按钮的正上方
        this.$nextTick(() => {
          const emojiButton = this.$refs.emojiButton.$el;
          const buttonRect = emojiButton.getBoundingClientRect();
          const containerRect = this.$el.getBoundingClientRect();

          // 计算左侧位置，使面板左对齐表情按钮
          // 相对于聊天容器的位置
          const left = buttonRect.left - containerRect.left;

          // 计算底部位置，使面板底部与表情按钮顶部对齐
          // 添加一个小的间距(8px)
          const inputArea = emojiButton.closest('.input-area');
          const inputAreaRect = inputArea.getBoundingClientRect();
          const bottom = containerRect.bottom - buttonRect.top + 8;

          this.emojiPanelPosition = {
            left: left,
            bottom: bottom,
          };
        });
      }
    },

    closeEmojiPanel() {
      this.showEmojiPanel = false;
    },

    selectEmojiCategory(categoryKey) {
      this.activeEmojiCategory = categoryKey;
    },

    insertEmoji(emoji) {
      // 添加到最近使用的表情中
      this.addToRecentEmojis(emoji);

      // 插入到输入框
      this.messageInput += emoji;

      // 关闭表情面板
      this.closeEmojiPanel();

      // 聚焦到输入框
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.focus();
        }
      });
    },

    addToRecentEmojis(emoji) {
      // 移除重复的表情
      const index = this.recentEmojis.indexOf(emoji);
      if (index > -1) {
        this.recentEmojis.splice(index, 1);
      }

      // 添加到最前面
      this.recentEmojis.unshift(emoji);

      // 限制最近使用的表情数量
      if (this.recentEmojis.length > 20) {
        this.recentEmojis = this.recentEmojis.slice(0, 20);
      }
    },

    // 修改发送消息方法，支持表情包
    sendMessage() {
      if (!this.messageInput.trim() || !this.currentFriend) return;

      const message = {
        id: Date.now(),
        content: this.messageInput.trim(),
        timestamp: Date.now(),
        isOwn: true,
        type: 'text',
      };

      if (!this.messages[this.currentFriend.sessionId]) {
        this.$set(this.messages, this.currentFriend.sessionId, []);
      }

      this.messages[this.currentFriend.sessionId].push(message);
      this.currentFriend.lastMessage = this.getLastMessagePreview(message.content);
      this.currentFriend.lastTime = this.formatTime(message.timestamp);

      this.messageInput = '';

      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 模拟接收消息
      setTimeout(
        () => {
          this.simulateReply();
        },
        1000 + Math.random() * 2000
      );
    },

    // 点击外部关闭表情面板
    handleClickOutside(event) {
      if (this.showEmojiPanel) {
        const emojiPanel = this.$refs.emojiPanel;
        const emojiButton = this.$refs.emojiButton;

        if (
          emojiPanel &&
          !emojiPanel.contains(event.target) &&
          emojiButton &&
          !emojiButton.$el.contains(event.target)
        ) {
          this.closeEmojiPanel();
        }
      }
    },
    // 获取当前用户在群中的角色
    getCurrentUserRole(group) {
      if (!group || !group.members) return '成员';
      const currentUserMember = group.members.find((m) => m.id === this.userInfo.id);
      if (!currentUserMember) return '成员';

      switch (currentUserMember.role) {
        case 'owner':
          return '群主';
        case 'admin':
          return '管理员';
        default:
          return '成员';
      }
    },
    // 获取显示名称（备注名优先，支持群备注）
    getDisplayName(friend) {
      if (!friend) return '未知用户';

      if (friend.type === 'group') {
        return this.groupRemarks[friend.id] || friend.name || '未知群聊';
      } else {
        return this.friendRemarks[friend.id] || friend.name || '未知用户';
      }
    },
    // 显示群信息
    showGroupInfo(group) {
      this.groupInfoData = { ...group };
      this.tempGroupName = group.name;
      this.tempGroupRemark = this.groupRemarks[group.id] || '';
      this.editingGroupName = false;
      this.editingGroupRemark = false;
      this.showGroupInfoDialog = true;
      this.hideContextMenu();
    },

    // 检查用户是否是群主
    isGroupOwner(group) {
      if (!group || !group.members) return false;
      const currentUserMember = group.members.find((m) => m.id === this.userInfo.id);
      return currentUserMember && currentUserMember.role === 'owner';
    },

    // 编辑群名称
    editGroupName() {
      if (!this.isGroupOwner(this.groupInfoData)) {
        this.$message.warning('只有群主可以修改群名称');
        return;
      }
      this.editingGroupName = true;
      this.$nextTick(() => {
        this.$refs.groupNameInput?.focus();
      });
    },

    // 保存群名称
    saveGroupName() {
      if (!this.groupInfoData || !this.tempGroupName.trim()) {
        this.$message.error('群名称不能为空');
        return;
      }

      if (!this.isGroupOwner(this.groupInfoData)) {
        this.$message.warning('只有群主可以修改群名称');
        return;
      }

      const oldName = this.groupInfoData.name;
      const newName = this.tempGroupName.trim();

      if (oldName === newName) {
        this.editingGroupName = false;
        return;
      }

      // 更新群名称
      this.groupInfoData.name = newName;
      const groupIndex = this.friends.findIndex((f) => f.id === this.groupInfoData.id);
      if (groupIndex > -1) {
        this.friends[groupIndex].name = newName;
      }

      // 发送群名称变更通知消息
      const notificationMessage = {
        id: Date.now(),
        content: `群名称由"${oldName}"变更为"${newName}"`,
        timestamp: Date.now(),
        isOwn: false,
        type: 'system',
      };

      if (!this.messages[this.groupInfoData.id]) {
        this.$set(this.messages, this.groupInfoData.id, []);
      }

      this.messages[this.groupInfoData.id].push(notificationMessage);

      // 更新最后一条消息
      if (groupIndex > -1) {
        this.friends[groupIndex].lastMessage = notificationMessage.content;
        this.friends[groupIndex].lastTime = this.formatTime(notificationMessage.timestamp);
      }

      this.editingGroupName = false;

      // 如果当前正在聊天的就是这个群，立即更新显示
      if (this.currentFriend && this.currentFriend.id === this.groupInfoData.id) {
        this.currentFriend.name = newName;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }

      this.$message.success('群名称修改成功');
    },

    // 取消编辑群名称
    cancelEditGroupName() {
      this.tempGroupName = this.groupInfoData?.name || '';
      this.editingGroupName = false;
    },

    // 编辑群备注
    editGroupRemark() {
      this.editingGroupRemark = true;
      this.$nextTick(() => {
        this.$refs.groupRemarkInput?.focus();
      });
    },

    // 保存群备注
    saveGroupRemark() {
      if (this.groupInfoData) {
        this.$set(this.groupRemarks, this.groupInfoData.id, this.tempGroupRemark);
        this.editingGroupRemark = false;

        // 如果当前正在聊天的就是这个群，立即更新显示
        if (this.currentFriend && this.currentFriend.id === this.groupInfoData.id) {
          this.$forceUpdate();
        }

        this.$message.success('群备注修改成功');
      }
    },

    // 取消编辑群备注
    cancelEditGroupRemark() {
      this.tempGroupRemark = this.groupRemarks[this.groupInfoData?.id] || '';
      this.editingGroupRemark = false;
    },

    // 修改右键菜单处理，支持群聊
    handleRightClick(friend, event) {
      event.preventDefault();
      this.contextMenuTarget = friend;
      this.contextMenuPosition = {
        x: event.clientX,
        y: event.clientY,
      };
      this.contextMenuVisible = true;

      // 点击其他地方隐藏菜单
      this.$nextTick(() => {
        document.addEventListener('click', this.hideContextMenu);
      });
    },

    // 修改显示信息方法，支持群聊
    showInfo(item) {
      if (item.type === 'group') {
        this.showGroupInfo(item);
      } else {
        this.showFriendInfo(item);
      }
    },

    // 退出群聊
    leaveGroup(group) {
      this.$confirm('确定要退出这个群聊吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const index = this.friends.findIndex((f) => f.id === group.id);
          if (index > -1) {
            this.friends.splice(index, 1);
            if (this.currentFriend && this.currentFriend.id === group.id) {
              this.currentFriend = null;
            }
            // 清除群备注
            this.$delete(this.groupRemarks, group.id);
            this.$message.success('已退出群聊');
            this.showGroupInfoDialog = false;
          }
        })
        .catch(() => {});

      if (this.contextMenuVisible) {
        this.hideContextMenu();
      }
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener('click', this.hideContextMenu);
    },

    // 显示好友信息
    showFriendInfo(friend) {
      this.friendInfoData = { ...friend };
      this.tempRemark = this.friendRemarks[friend.id] || '';
      this.editingRemark = false;
      this.showFriendInfoDialog = true;
      this.hideContextMenu();
    },

    // 编辑备注名
    editRemark() {
      this.editingRemark = true;
      this.$nextTick(() => {
        this.$refs.remarkInput?.focus();
      });
    },

    // 取消编辑备注
    cancelEditRemark() {
      this.tempRemark = this.friendRemarks[this.friendInfoData?.id] || '';
      this.editingRemark = false;
    },

    selectFriend(friend) {
      this.currentFriend = friend;
      friend.unreadCount = 0;
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    toggleSection(sectionName) {
      this.$set(this.expandedSections, sectionName, !this.expandedSections[sectionName]);
    },

    // 发送好友申请
    sendFriendRequest() {
      this.$refs.addFriendForm.validate((valid) => {
        if (valid) {
          const friendInfo = {
            ...this.addFriendForm,
            applyId: this.userInfo.id,
          };
          sendFriendRequest(friendInfo).then((res) => {
            if (res.message === 'friendExists') {
              this.$message.error(res.data);
            } else if (res.message === 'userNoExists') {
              this.$message.error(res.data);
            } else {
              this.$message.success('好友申请已发送！');
              this.showAddFriendDialog = false;
              this.addFriendForm = {
                friendId: '',
                message: '我是你的好友',
              };
            }
          });
        }
      });
    },

    // 接受好友申请
    acceptFriendRequest(request) {
      acceptFriendRequest(request).then((res) => {
        if (res.data === 'SUCCESS') {
          // // 从申请列表中移除
          const index = this.friendRequests.findIndex((req) => req.id === request.id);
          if (index > -1) {
            this.friendRequests.splice(index, 1);
          }

          this.$message.success(`已同意 ${request.name} 的好友申请`);
        }
        // if (res.data === 'SUCCESS_ALREADY') {
        //   // // 从申请列表中移除
        //   const index = this.friendRequests.findIndex((req) => req.id === request.id);
        //   if (index > -1) {
        //     this.friendRequests.splice(index, 1);
        //   }

        //   this.$message.success(`${request.name} 已经是你的好友了`);
        // }
      });

      // 添加到好友列表
      // const newFriend = {
      //   id: request.id,
      //   name: request.name,
      //   avatar: request.avatar,
      //   lastMessage: '',
      //   lastTime: '',
      //   unreadCount: 0,
      //   online: true,
      //   type: 'user',
      // };
      // this.friends.push(newFriend);
    },

    // 拒绝好友申请
    rejectFriendRequest(request) {
      rejectFriendRequest(request).then((res) => {
        if (res.data === 'SUCCESS') {
          const index = this.friendRequests.findIndex((req) => req.id === request.id);
          if (index > -1) {
            this.friendRequests.splice(index, 1);
          }
          this.$message.info(`已拒绝 ${request.name} 的好友申请`);
        }
      });
    },

    // 创建群聊
    createGroup() {
      this.$refs.createGroupForm.validate((valid) => {
        if (valid) {
          createGroup(this.createGroupForm).then((res) => {
            if (res.data === 'CREATE_GROUP_SUCCESS') {
              this.$message.success('群聊创建成功！');
              this.showCreateGroupDialog = false;
              this.createGroupForm = {
                groupName: '',
                groupDesc: '',
                selectedFriends: [],
              };
            }
          });
          // const newGroup = {
          //   id: 'group' + Date.now(),
          //   name: this.createGroupForm.groupName,
          //   avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
          //   lastMessage: '群聊已创建',
          //   lastTime: this.formatTime(Date.now()),
          //   unreadCount: 0,
          //   online: true,
          //   type: 'group',
          //   memberCount: this.createGroupForm.selectedFriends.length + 1,
          //   members: [
          //     {
          //       id: this.userInfo.id,
          //       name: this.userInfo.name,
          //       avatar: this.userInfo.avatar,
          //       role: 'owner',
          //     },
          //     ...this.createGroupForm.selectedFriends.map((friendId) => {
          //       const friend = this.friends.find((f) => f.id === friendId);
          //       return {
          //         id: friend.id,
          //         name: friend.name,
          //         avatar: friend.avatar,
          //         role: 'member',
          //       };
          //     }),
          //   ],
          // };

          // this.friends.push(newGroup);
          // this.$set(this.messages, newGroup.id, [
          //   {
          //     id: Date.now(),
          //     content: `群聊"${newGroup.name}"已创建`,
          //     timestamp: Date.now(),
          //     isOwn: false,
          //     type: 'system',
          //   },
          // ]);

          //   this.$message.success('群聊创建成功！');
          //   this.showCreateGroupDialog = false;
          //   this.createGroupForm = {
          //     groupName: '',
          //     groupDesc: '',
          //     selectedFriends: [],
          //   };
        }
      });
    },

    // 发送加入群聊申请
    sendJoinGroupRequest() {
      this.$refs.joinGroupForm.validate((valid) => {
        if (valid) {
          const friendInfo = {
            ...this.joinGroupForm,
            applyId: this.userInfo.id,
          };
          sendGroupRequest(friendInfo).then((res) => {
            if (res.message === 'isJoinGroup') {
              this.$message.error(res.data);
            } else if (res.message === 'groupNotExists') {
              this.$message.error(res.data);
            } else {
              this.$message.success('入群申请已发送！');
              this.showJoinGroupDialog = false;
              this.joinGroupForm = {
                groupId: '',
                reason: '希望加入群聊',
              };
            }
          });
        }
      });
    },

    // 接受群聊申请
    acceptGroupRequest(request) {
      acceptGroupRequest(request).then((res) => {
        if (res.data === 'ACCEPT_SUCCESS') {
          // // 从申请列表中移除
          const index = this.groupRequests.findIndex((req) => req.id === request.id);
          if (index > -1) {
            this.groupRequests.splice(index, 1);
          }

          this.$message.success(`已同意 ${request.applicantName} 的入群申请`);
        }
      });
    },

    // 拒绝群聊申请
    rejectGroupRequest(request) {
      rejectGroupRequest(request).then((res) => {
        if (res.data === 'SUCCESS') {
          const index = this.groupRequests.findIndex((req) => req.id === request.id);
          if (index > -1) {
            this.groupRequests.splice(index, 1);
          }
          this.$message.info(`已拒绝 ${request.applicantName} 的入群申请`);
        }
      });
    },
    // 修改模拟回复方法，支持文件回复
    simulateReply() {
      if (!this.currentFriend) return;

      setTimeout(() => {
        const replies = [
          { type: 'text', content: '收到了！' },
          { type: 'text', content: '好的，知道了' },
          { type: 'text', content: '没问题' },
          { type: 'text', content: '哈哈哈' },
          { type: 'text', content: '👍' },
          { type: 'text', content: '稍等一下' },
          { type: 'text', content: '在忙，晚点回复你' },
        ];

        // 随机选择回复类型，有10%概率回复文件
        const isFileReply = Math.random() < 0.1;

        let replyMessage;

        if (isFileReply) {
          // 模拟文件回复
          const fileTypes = [
            { name: '项目计划.docx', size: 1024 * 1024 * 2.5 },
            { name: '财务报表.xlsx', size: 1024 * 512 },
            { name: '会议记录.pdf', size: 1024 * 1024 * 1.2 },
            { name: '产品说明.pptx', size: 1024 * 1024 * 3.7 },
            { name: '源代码.zip', size: 1024 * 1024 * 5.1 },
          ];

          const randomFile = fileTypes[Math.floor(Math.random() * fileTypes.length)];

          if (this.currentFriend.type === 'group') {
            // 群聊回复，随机选择一个群成员
            const members = this.currentFriend.members
              ? this.currentFriend.members.filter((m) => m.id !== this.userInfo.id)
              : [];
            if (members.length > 0) {
              const randomMember = members[Math.floor(Math.random() * members.length)];
              replyMessage = {
                id: Date.now(),
                type: 'file',
                fileName: randomFile.name,
                fileSize: randomFile.size,
                fileType: randomFile.name.split('.').pop(),
                timestamp: Date.now(),
                isOwn: false,
                senderId: randomMember.id,
                senderName: randomMember.name,
                senderAvatar: randomMember.avatar,
              };
            }
          } else {
            // 私聊回复
            replyMessage = {
              id: Date.now(),
              type: 'file',
              fileName: randomFile.name,
              fileSize: randomFile.size,
              fileType: randomFile.name.split('.').pop(),
              timestamp: Date.now(),
              isOwn: false,
            };
          }
        } else {
          // 文本回复
          const randomReply = replies[Math.floor(Math.random() * replies.length)];

          if (this.currentFriend.type === 'group') {
            // 群聊回复，随机选择一个群成员
            const members = this.currentFriend.members
              ? this.currentFriend.members.filter((m) => m.id !== this.userInfo.id)
              : [];
            if (members.length > 0) {
              const randomMember = members[Math.floor(Math.random() * members.length)];
              replyMessage = {
                id: Date.now(),
                content: randomReply.content,
                timestamp: Date.now(),
                isOwn: false,
                type: 'text',
                senderId: randomMember.id,
                senderName: randomMember.name,
                senderAvatar: randomMember.avatar,
              };
            }
          } else {
            // 私聊回复
            replyMessage = {
              id: Date.now(),
              content: randomReply.content,
              timestamp: Date.now(),
              isOwn: false,
              type: 'text',
            };
          }
        }

        if (replyMessage && this.messages[this.currentFriend.sessionId]) {
          this.messages[this.currentFriend.sessionId].push(replyMessage);

          // 更新最后一条消息预览
          if (replyMessage.type === 'file') {
            this.currentFriend.lastMessage = this.getLastMessagePreview(
              '',
              'file',
              replyMessage.fileName
            );
          } else {
            this.currentFriend.lastMessage = this.getLastMessagePreview(
              replyMessage.content,
              replyMessage.type
            );
          }

          this.currentFriend.lastTime = this.formatTime(replyMessage.timestamp);

          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      }, 2000);
    },

    // 处理键盘事件
    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        this.sendMessage();
      }
    },

    // 选择图片
    selectImage() {
      this.$refs.imageInput.click();
    },

    // 处理图片上传
    handleImageUpload(event) {
      const file = event.target.files[0];
      if (!file || !this.currentFriend) return;

      // 这里应该上传到服务器，现在模拟一个URL
      const reader = new FileReader();
      reader.onload = (e) => {
        const message = {
          id: Date.now(),
          content: e.target.result,
          timestamp: Date.now(),
          isOwn: true,
          type: 'image',
        };

        if (!this.messages[this.currentFriend.sessionId]) {
          this.$set(this.messages, this.currentFriend.sessionId, []);
        }

        this.messages[this.currentFriend.sessionId].push(message);
        this.currentFriend.lastMessage = '[图片]';
        this.currentFriend.lastTime = this.formatTime(message.timestamp);

        this.$nextTick(() => {
          this.scrollToBottom();
        });
      };
      reader.readAsDataURL(file);

      // 清空input
      event.target.value = '';
    },

    // 预览图片
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl;
      this.imagePreviewVisible = true;
    },

    // 滚动到底部
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      const timeZone = 'Asia/Shanghai'; // 中国时区

      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;

      if (diff < 60000) {
        // 1分钟内
        return '刚刚';
      } else if (diff < 3600000) {
        // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
      } else if (this.isSameDay(date, now, timeZone)) {
        // 今天
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          timeZone: timeZone,
        });
      } else if (this.isYesterday(date, now, timeZone)) {
        // 昨天
        return (
          '昨天 ' +
          date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: timeZone,
          })
        );
      } else if (this.isSameYear(date, now, timeZone)) {
        // 本年内（但不是今天和昨天）
        return date.toLocaleDateString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          timeZone: timeZone,
        });
      } else {
        // 非本年
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          timeZone: timeZone,
        });
      }
    },
    // 辅助方法：判断是否同一天
    isSameDay(date1, date2, timeZone) {
      const fmt = new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: timeZone,
      });
      return fmt.format(date1) === fmt.format(date2);
    },

    // 辅助方法：判断是否是昨天
    isYesterday(date, now, timeZone) {
      const yesterday = new Date(now.getTime() - 86400000);
      return this.isSameDay(date, yesterday, timeZone);
    },

    // 辅助方法：判断是否同一年
    isSameYear(date1, date2, timeZone) {
      const fmt = new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        timeZone: timeZone,
      });
      return fmt.format(date1) === fmt.format(date2);
    },
    // 获取群成员显示名称
    // getMemberDisplayName(member) {
    //   return this.friendRemarks[member.id] || member.name || '未知用户';
    // },
    getMemberDisplayName(member) {
      if (!member) return '未知用户';
      return this.friendRemarks[member.id] || member.name || '未知用户';
    },

    // 获取群成员备注
    getMemberRemark(member) {
      return this.friendRemarks[member.id];
    },

    // 复制用户ID
    copyUserId(userId) {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard
          .writeText(userId)
          .then(() => {
            this.$message.success('用户ID已复制到剪贴板');
          })
          .catch(() => {
            this.fallbackCopyUserId(userId);
          });
      } else {
        this.fallbackCopyUserId(userId);
      }
    },

    // 备用复制方法
    fallbackCopyUserId(userId) {
      const textArea = document.createElement('textarea');
      textArea.value = userId;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        this.$message.success('用户ID已复制到剪贴板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }

      document.body.removeChild(textArea);
    },

    // 保存备注名（修改后更新聊天页面）
    saveRemark() {
      if (this.friendInfoData) {
        this.$set(this.friendRemarks, this.friendInfoData.id, this.tempRemark);
        this.editingRemark = false;

        // 如果当前正在聊天的就是这个好友，立即更新显示
        if (this.currentFriend && this.currentFriend.id === this.friendInfoData.id) {
          this.$forceUpdate();
        }

        this.$message.success('备注修改成功');
      }
    },

    // 修改删除好友方法，从对话框中删除
    deleteFriend(friend) {
      this.$confirm('确定要删除这个好友吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const index = this.friends.findIndex((f) => f.id === friend.id);
          if (index > -1) {
            this.friends.splice(index, 1);
            if (this.currentFriend && this.currentFriend.id === friend.id) {
              this.currentFriend = null;
            }
            // 清除备注
            this.$delete(this.friendRemarks, friend.id);
            this.$message.success('好友已删除');
            this.showFriendInfoDialog = false;
          }
        })
        .catch(() => {});

      if (this.contextMenuVisible) {
        this.hideContextMenu();
      }
    },
  },
};
</script>

<style scoped>
/* 基础容器样式 */
/* .chat-container {
  display: flex;
  height: 100%;
  background: #f5f6fa;
} */
/* 聊天容器需要相对定位，作为表情面板的参考点 */
.chat-container {
  position: relative;
  display: flex;
  height: 100%;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  width: 380px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.sidebar-tabs {
  margin-bottom: 20px;
}

.sidebar-tabs .el-tabs__header {
  margin: 0;
}

.tab-badge {
  margin-left: 5px;
}

.search-input {
  width: 100%;
}

/* 内容列表样式 */
.content-list {
  flex: 1;
  overflow-y: auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: bold;
}

.create-group-btn {
  padding: 0;
  font-size: 12px;
}

/* 好友列表样式 */
.friends-list {
  padding: 0;
}

.friend-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f8f8f8;
}

.friend-item:hover {
  background: #f0f2f5;
}

.friend-item.active {
  background: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.friend-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.friend-info {
  flex: 1;
  min-width: 0;
}

.friend-name {
  font-weight: 500;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.last-message {
  color: #999;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.friend-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.time {
  font-size: 12px;
  color: #999;
}

/* 通讯录样式 */
.contact-sections {
  padding: 10px 0;
}

.contact-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  background: #fafafa;
  color: #333;
  font-weight: 500;
}

.section-header:hover {
  background: #f0f0f0;
}

.section-header i:first-child {
  margin-right: 10px;
  color: #1890ff;
}

.section-header i.el-icon-arrow-right {
  margin-left: auto;
  transition: transform 0.3s;
}

.section-header i.el-icon-arrow-right.expanded {
  transform: rotate(90deg);
}

.section-badge {
  margin-left: 10px;
}

.section-content {
  background: white;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: #999;
}

/* 申请列表样式 */
.request-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.request-item:last-child {
  border-bottom: none;
}

.request-info {
  flex: 1;
  margin-left: 12px;
}

.request-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.request-message {
  color: #666;
  font-size: 13px;
}

.request-actions {
  display: flex;
  gap: 8px;
}

/* 操作功能样式 */
.action-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-item:hover {
  background: #f0f2f5;
}

.action-item i {
  margin-right: 10px;
  color: #1890ff;
}

/* 聊天区域样式 */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.no-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}

.no-chat i {
  font-size: 64px;
  margin-bottom: 20px;
}

.chat-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 聊天头部样式 */
.chat-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: white;
}

.chat-title {
  margin-left: 15px;
  flex: 1;
}

.chat-title h4 {
  margin: 0 0 5px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.online-status {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-dot.online {
  background: #52c41a;
}

.status-dot.offline {
  background: #999;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

/* 消息列表样式 */
.messages-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #fafafa;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  margin: 0 10px;
}

.message-content {
  max-width: 60%;
  display: flex;
  flex-direction: column;
}

.own-message .message-content {
  align-items: flex-end;
}

.sender-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  padding: 0 12px;
}

/* .message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  margin-bottom: 5px;
  word-wrap: break-word;
} */
/* 确保消息气泡样式正确 */
.message-bubble {
  border-radius: 18px;
  overflow: hidden;
  word-break: break-word;
}
/* 确保文件消息在消息气泡中正确显示 */
.message-bubble .file-message {
  margin: 0;
  border-radius: 12px !important;
}

/* 非自己发送的消息气泡 */
.message-item:not(.own-message) .message-bubble {
  background: transparent;
  border: none;
}

/* 非自己发送的消息气泡 */
.message-item:not(.own-message) .message-bubble {
  background: transparent;
  border: none;
}

/* 确保文本消息样式正确 */
.text-message {
  padding: 12px 16px;
  line-height: 1.4;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 18px;
}
.own-message .text-message {
  background: #1890ff;
  color: white;
  border: none;
}

/* .system-message {
  background: #f0f0f0 !important;
  color: #666 !important;
  border: none !important;
  text-align: center;
  font-size: 12px;
} */
/* 确保系统消息样式正确 */
/* .system-message {
  padding: 8px 12px;
  background: #f0f0f0 !important;
  color: #666 !important;
  border: none !important;
  text-align: center;
  font-size: 12px;
  border-radius: 12px !important;
} */
/* 系统消息样式 */
.system-message {
  background-color: #f0f0f0;
  color: #666;
  border-radius: 12px;
  padding: 6px 12px;
  font-size: 12px;
  text-align: center;
  max-width: 70%;
  display: inline-block;
}
.system-message .message-time {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
  text-align: center;
}
/* 确保普通消息样式不受影响 */
.message-item:not(.system-message-container) {
  display: flex;
  margin-bottom: 20px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}
/* 确保图片消息样式正确 */
.image-message {
  padding: 4px;
}

.image-message img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
}

.message-time {
  font-size: 12px;
  color: #999;
  padding: 0 5px;
}

/* 输入区域样式 */
.input-area {
  border-top: 1px solid #e8e8e8;
  background: white;
  padding: 10px;
  position: relative;
}

.input-toolbar {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;
  padding: 0 5px;
}

.input-toolbar .el-button {
  padding: 5px;
  color: #666;
}

.input-toolbar .el-button:hover {
  color: #409eff;
  background: #f0f9ff;
}

/* 表情包面板 */
/* 表情包面板 */
.emoji-panel {
  position: absolute;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 250px;
  width: 320px;
  display: flex;
  flex-direction: column;
}

.emoji-categories {
  display: flex;
  overflow-x: auto;
  border-bottom: 1px solid #ebeef5;
  padding: 3px; /* 减小内边距，从5px改为3px */
}

.category-tab {
  padding: 6px 10px; /* 减小内边距，从8px 12px改为6px 10px */
  cursor: pointer;
  font-size: 12px; /* 减小字体大小，从13px改为12px */
  white-space: nowrap;
  color: #606266;
}

.category-tab.active {
  color: #409eff;
  font-weight: 500;
  background: #ecf5ff;
  border-radius: 4px;
}

.emoji-content {
  flex: 1;
  overflow-y: auto;
  padding: 3px; /* 减小内边距，从5px改为3px */
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr); /* 改为7列，使每个表情更小 */
  gap: 2px; /* 进一步减小间距，从4px改为2px */
  padding: 5px; /* 减小内边距，从8px改为5px */
}

.emoji-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px; /* 减小宽度，从32px改为28px */
  height: 28px; /* 减小高度，从32px改为28px */
  font-size: 18px; /* 减小字体大小，从20px改为18px */
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.emoji-item:hover {
  background-color: #f0f0f0;
  transform: scale(1.1); /* 减小悬停时的放大效果，从1.2改为1.1 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .emoji-panel {
    width: 280px; /* 在小屏幕上进一步减小宽度 */
    max-height: 220px; /* 在小屏幕上减小最大高度 */
  }

  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 2px;
    padding: 4px;
  }

  .emoji-item {
    width: 26px;
    height: 26px;
    font-size: 16px;
  }

  .category-tab {
    padding: 5px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .emoji-panel {
    width: 240px; /* 在更小的屏幕上进一步减小宽度 */
    max-height: 200px;
  }

  .emoji-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 2px;
  }

  .emoji-item {
    width: 24px;
    height: 24px;
    font-size: 15px;
  }
}

/* 消息输入框和发送按钮容器 */
.message-input-container {
  display: flex;
  align-items: flex-end;
  gap: 10px;
}

.message-input-container .el-textarea {
  flex: 1;
}

.message-input-container .el-textarea__inner {
  resize: none;
  border-radius: 4px;
  padding: 8px 12px;
  min-height: 80px;
  max-height: 120px;
}

.message-input-container .el-button {
  height: 40px;
  padding: 0 20px;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    padding: 8px;
  }

  .emoji-item {
    width: 28px;
    height: 28px;
    font-size: 18px;
  }

  .category-tab {
    padding: 6px 10px;
    font-size: 11px;
  }

  .message-input-container {
    flex-direction: column;
    align-items: stretch;
  }

  .message-input-container .el-button {
    margin-top: 8px;
    height: 36px;
  }
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

.friend-checkbox {
  display: flex !important;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
}

.friend-checkbox .el-avatar {
  margin-right: 8px;
  margin-left: 8px;
}

/* 群成员样式 */
.group-members {
  max-height: 400px;
  overflow-y: auto;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-info {
  flex: 1;
  margin-left: 12px;
}

.member-name {
  font-weight: 500;
  margin-bottom: 3px;
}

.member-role {
  font-size: 12px;
  color: #999;
}

.member-badge {
  margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
  }

  .message-content {
    max-width: 80%;
  }

  .friend-item {
    padding: 12px 15px;
  }

  .chat-header {
    padding: 12px 15px;
  }

  .messages-container {
    padding: 15px;
  }

  .input-box {
    padding: 0 15px 15px 15px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 2000;
  min-width: 120px;
}

.context-menu-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item:first-child {
  border-radius: 4px 4px 0 0;
}

.context-menu-item:last-child {
  border-radius: 0 0 4px 4px;
}

/* 在线状态点 */
.online-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #67c23a;
  margin-left: 5px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #909399;
  margin-right: 5px;
}

.status-dot.online {
  background-color: #67c23a;
}

/* 好友信息对话框样式 */
.friend-info-dialog .el-dialog__body {
  padding: 20px;
}

.friend-info-content {
  max-height: 60vh;
  overflow-y: auto;
}

.friend-header {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;
}

.friend-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.friend-details {
  margin-left: 20px;
  flex: 1;
}

.friend-details p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.online-status {
  display: flex;
  align-items: center;
}

.friend-extra-info {
  padding: 0;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 14px;
}

.info-item label {
  width: 80px;
  color: #666;
  flex-shrink: 0;
}

.remark-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.remark-display {
  flex: 1;
}

.remark-input {
  flex: 1;
  max-width: 200px;
}

.remark-actions {
  display: flex;
  gap: 5px;
}

/* 聊天头部样式 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.chat-header-info {
  display: flex;
  align-items: center;
}

.header-text {
  margin-left: 12px;
}

.chat-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-subtitle {
  font-size: 12px;
  color: #999;
}

.chat-header-actions {
  display: flex;
  gap: 10px;
}

/* 好友信息对话框新增样式 */
.real-name {
  font-size: 13px;
  color: #666;
  margin: 3px 0 !important;
}

.user-id {
  font-family: 'Courier New', monospace;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 13px;
}

.friend-actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

/* 群成员样式调整 */
.member-real-name {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.member-info {
  flex: 1;
  margin-left: 12px;
}

.member-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.member-role {
  font-size: 12px;
  color: #999;
}

.member-badge {
  margin-left: auto;
}

/* 信息项样式调整 */
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
  min-height: 24px;
}

.info-item label {
  width: 80px;
  color: #666;
  flex-shrink: 0;
}

.remark-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.remark-display {
  flex: 1;
  color: #333;
}

.remark-input {
  flex: 1;
  max-width: 200px;
}

.remark-actions {
  display: flex;
  gap: 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 15px;
  }

  .chat-title {
    font-size: 15px;
  }

  .friend-info-dialog {
    width: 90% !important;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .info-item label {
    width: auto;
    font-weight: 500;
  }

  .remark-section {
    width: 100%;
  }

  .remark-input {
    max-width: none;
  }
}
/* 群信息对话框样式 */
.group-info-dialog .el-dialog__body {
  padding: 20px;
}

.group-info-content {
  max-height: 60vh;
  overflow-y: auto;
}

.group-header {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;
}

.group-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.group-details {
  margin-left: 20px;
  flex: 1;
}

.group-details p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.group-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.group-extra-info {
  padding: 0;
}

.group-id {
  font-family: 'Courier New', monospace;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 13px;
}

.name-section,
.remark-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.name-display,
.remark-display {
  flex: 1;
  color: #333;
}

.name-input,
.remark-input {
  flex: 1;
  max-width: 200px;
}

.name-actions,
.remark-actions {
  display: flex;
  gap: 5px;
}

.permission-hint {
  font-size: 12px;
  color: #999;
}

.group-actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.real-name-hint {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}

/* 聊天头部可点击样式 */
.chat-header-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.chat-header-info:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 系统消息样式（用于群名称变更通知） */
.message-item.system .message-content {
  background: #f0f9ff;
  color: #1890ff;
  border: 1px solid #d4edda;
  text-align: center;
  font-size: 13px;
  padding: 8px 12px;
  margin: 10px auto;
  max-width: 80%;
  border-radius: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .group-info-dialog {
    width: 90% !important;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .info-item label {
    width: auto;
    font-weight: 500;
  }

  .name-section,
  .remark-section {
    width: 100%;
  }

  .name-input,
  .remark-input {
    max-width: none;
  }

  .group-actions {
    flex-direction: column;
  }
}
/* 通讯录容器样式 */
.contacts-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.contacts-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.contacts-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.contacts-actions .el-button {
  flex: 1;
  max-width: 120px;
}

/* 联系人分组样式 */
.contact-sections {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.contact-section {
  border-bottom: 1px solid #f5f5f5;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s;
  position: sticky;
  top: 0;
  z-index: 1;
}

.section-header:hover {
  background: #f0f1f2;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.section-icon {
  font-size: 16px;
  color: #409eff;
}

.section-badge {
  margin-left: 5px;
}

.friend-count {
  font-size: 12px;
  color: #999;
  font-weight: normal;
}

.section-arrow {
  transition: transform 0.3s;
  color: #999;
}

.section-arrow.expanded {
  transform: rotate(90deg);
}

/* 分组内容样式 */
.section-content {
  background: white;
}

/* 空状态样式 */
.empty-state {
  padding: 30px 20px;
  text-align: center;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.empty-state i {
  font-size: 24px;
  color: #ddd;
}

/* 申请项样式 */
.request-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 20px;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.3s;
}

.request-item:hover {
  background: #f8f9fa;
}

.request-item:last-child {
  border-bottom: none;
}

.request-info {
  flex: 1;
  margin-left: 12px;
}

.request-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.request-message {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.request-time {
  color: #999;
  font-size: 12px;
}

.request-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

/* 群聊申请特殊样式 */
/* .group-request-item .request-info {
  margin-left: 12px;
} */
.group-request-item {
  display: block;
  padding: 12px 20px;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.3s;
}

.group-request-avatars {
  display: flex;
  align-items: center;
  gap: 8px;
}

.arrow-icon {
  color: #999;
  font-size: 12px;
}

.group-name {
  color: #409eff;
  font-size: 13px;
  margin-bottom: 2px;
}

/* 好友项紧凑样式 */
.friend-item.compact {
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid #f8f9fa;
}

.friend-item.compact:hover {
  background: #f8f9fa;
}

.friend-item.compact:last-child {
  border-bottom: none;
}

.friend-item.compact .friend-info {
  margin-left: 12px;
  flex: 1;
}

.friend-item.compact .friend-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.friend-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.user-role {
  color: #409eff;
}

/* .friend-actions {
  opacity: 0;
  transition: opacity 0.3s;
} */

.friend-item.compact:hover .friend-actions {
  opacity: 1;
}

.real-name-small {
  font-size: 12px;
  color: #999;
  font-weight: normal;
}

/* 状态点样式 */
.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ddd;
}

.status-dot.online {
  background-color: #67c23a;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .contacts-header {
    padding: 12px 15px;
  }

  .section-header {
    padding: 12px 15px;
  }

  .request-item,
  .friend-item.compact {
    padding: 12px 15px;
  }

  .contacts-actions {
    flex-direction: column;
  }

  .contacts-actions .el-button {
    max-width: none;
  }
}

/* 滚动条优化 */
.contact-sections::-webkit-scrollbar {
  width: 4px;
}

.contact-sections::-webkit-scrollbar-track {
  background: transparent;
}

.contact-sections::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 2px;
}

.contact-sections::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

.group-request-item:hover {
  background: #f8f9fa;
}

.group-request-item:last-child {
  border-bottom: none;
}

.group-request-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.group-request-title {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.applicant-name {
  font-weight: 500;
  color: #409eff;
}

.group-name-inline {
  font-weight: 500;
  color: #333;
  background: #f0f9ff;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 13px;
}

.request-time-inline {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.group-request-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  margin-left: 32px; /* 对齐头像右侧 */
}

.request-reason {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 4px;
  font-size: 13px;
  color: #666;
  line-height: 1.3;
  min-height: 20px;
}

.reason-icon {
  font-size: 12px;
  color: #ccc;
  margin-top: 2px;
  flex-shrink: 0;
}

.request-actions.compact {
  display: flex;
  gap: 6px;
  margin: 0;
  flex-shrink: 0;
}

.request-actions.compact .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 好友申请样式保持原样但稍作调整 */
.request-item:not(.group-request-item) {
  display: flex;
  align-items: flex-start;
  padding: 15px 20px;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.3s;
}

.request-item:not(.group-request-item) .request-info {
  flex: 1;
  margin-left: 12px;
}

.request-item:not(.group-request-item) .request-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.request-item:not(.group-request-item) .request-message {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.request-item:not(.group-request-item) .request-time {
  color: #999;
  font-size: 12px;
}

.request-item:not(.group-request-item) .request-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

/* 移除之前的群聊申请样式 */
.group-request-avatars {
  display: none;
}

.arrow-icon {
  display: none;
}

.group-name {
  display: none;
}

/* 响应式优化 */
/* @media (max-width: 768px) {
  .group-request-item {
    padding: 10px 15px;
  }
  
  .group-request-content {
    flex-direction: column;
    gap: 8px;
    margin-left: 28px;
  }
  
  .request-actions.compact {
    align-self: flex-start;
  }
  
  .group-request-title {
    font-size: 13px;
  }
  
  .request-reason {
    font-size: 12px;
  }
} */
/* 响应式优化 */
@media (max-width: 768px) {
  .emoji-panel {
    max-height: 250px;
  }

  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    padding: 8px;
  }

  .emoji-item {
    width: 28px;
    height: 28px;
    font-size: 18px;
  }

  .category-tab {
    padding: 6px 10px;
    font-size: 11px;
  }
}

/* @media (max-width: 480px) {
  .group-request-header {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .request-time-inline {
    width: 100%;
    text-align: left;
    margin-top: 2px;
  }
  
  .group-request-content {
    margin-left: 0;
    padding-left: 28px;
  }
} */
@media (max-width: 480px) {
  .emoji-grid {
    grid-template-columns: repeat(5, 1fr);
  }

  .input-area {
    flex-direction: column;
    gap: 8px;
  }

  .input-area .el-button {
    align-self: flex-end;
  }
}

/* 统一Request Item的hover效果 */
.request-item:hover {
  background: #f8f9fa;
}

/* 优化按钮样式 */
.request-actions .el-button--mini {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 4px;
}

.request-actions.compact .el-button--mini {
  padding: 3px 8px;
}

/* 统一字体和间距 */
.contact-section {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.section-content {
  background: #fff;
}

/* 确保内容不会过度拉伸 */
.group-request-item {
  max-width: 100%;
  overflow: hidden;
}

.request-reason {
  word-break: break-word;
  overflow-wrap: break-word;
}

.input-toolbar .el-button {
  padding: 5px;
  color: #666;
}
.input-toolbar .el-button:hover {
  color: #409eff;
  background: #f0f9ff;
}
/* 表情包面板 */
.emoji-panel {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-bottom: 8px;
  max-height: 250px; /* 减小最大高度，从300px改为250px */
  width: 320px; /* 设置固定宽度，而不是占满整个容器 */
  display: flex;
  flex-direction: column;
}

/* 表情分类标签 */
.emoji-categories {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
  overflow-x: auto;
}
.category-tab {
  padding: 8px 12px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}
.category-tab:hover {
  color: #409eff;
  background: #f0f9ff;
}
.category-tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background: white;
}

/* 表情内容区域 */
.emoji-content {
  flex: 1;
  overflow-y: auto;
  max-height: 200px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 12px;
}

.emoji-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.emoji-item:hover {
  background: #f0f9ff;
  transform: scale(1.2);
}
/* 滚动条样式 */
.emoji-content::-webkit-scrollbar {
  width: 4px;
}

.emoji-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.emoji-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.emoji-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.emoji-categories::-webkit-scrollbar {
  height: 4px;
}

.emoji-categories::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.emoji-categories::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}
/* 文件消息样式 */
.file-message {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  max-width: 300px;
}

/* 图片和视频预览样式 */
.file-message.media-file {
  display: block;
  padding: 0;
  overflow: hidden;
  max-width: 300px;
}

.image-preview {
  width: 100%;
  position: relative;
}

.image-preview img {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 8px;
  cursor: pointer;
  display: block;
}

.video-preview {
  width: 100%;
  position: relative;
}

.video-preview video {
  width: 100%;
  max-height: 200px;
  border-radius: 8px;
  background-color: #000;
  display: block;
}

/* 调整图片和视频在消息气泡中的显示 */
.message-bubble .image-preview,
.message-bubble .video-preview {
  margin: -12px -16px;
}

/* 确保图片和视频在自己发送的消息中也有正确的样式 */
.own-message .file-message.media-file {
  background-color: transparent;
}

.message-item:not(.own-message) .file-message {
  background-color: #f9f9f9;
}

.own-message .file-message {
  background-color: rgba(24, 144, 255, 0.1);
}

.file-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: #e1e1e1;
  border-radius: 4px;
  margin-right: 10px;
  flex-shrink: 0;
}
/* 文件图标加载状态 */
.file-icon.loading {
  background-color: rgba(24, 144, 255, 0.1);
  animation: pulse 1.5s infinite ease-in-out;
}
.file-icon.loading i {
  font-size: 20px;
  color: #1890ff;
}

.own-message .file-icon.loading {
  background-color: rgba(24, 144, 255, 0.2);
}

.file-icon i {
  font-size: 24px;
  color: #606266;
}

.own-message .file-icon {
  background-color: rgba(24, 144, 255, 0.2);
}

.own-message .file-icon i {
  color: #1890ff;
}

.file-info {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.own-message .file-name {
  color: #1890ff;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-download {
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}

/* 下载图标 */
.file-download i {
  font-size: 20px;
  color: #1890ff;
}

/* 自己发送的下载图标 */
.own-message .file-download i {
  color: #0050b3;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 添加成员操作按钮样式 */
.member-actions {
  margin-left: auto;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}
/* 系统消息容器样式 */
.system-message-container {
  display: flex;
  justify-content: center;
  margin: 15px 0;
  width: 100%;
}
</style>
